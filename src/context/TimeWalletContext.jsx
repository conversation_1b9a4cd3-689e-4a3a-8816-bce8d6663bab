import React, { createContext, useContext, useReducer, useEffect } from 'react';

// Constants
const DAILY_START_HOUR = 6; // 6:00 AM
const DAILY_END_HOUR = 23; // 11:59 PM
const DAILY_MINUTES = (DAILY_END_HOUR - DAILY_START_HOUR + 1) * 60; // 18 hours = 1080 minutes
const DAILY_SECONDS = DAILY_MINUTES * 60; // 64,800 seconds
const STARTING_BALANCE = 10000;
const DEDUCTION_INTERVAL = 6.48; // seconds
const DEDUCTION_AMOUNT = 1; // dollar

// Session types
export const SESSION_TYPES = {
  FOCUS: 'focus',
  SHORT_BREAK: 'short_break',
  LONG_BREAK: 'long_break',
  IDLE: 'idle'
};

// Initial state
const initialState = {
  balance: STARTING_BALANCE,
  currentSession: SESSION_TYPES.IDLE,
  sessionStartTime: null,
  sessionDuration: 0,
  dailyStats: {
    focusTime: 0,
    shortBreakTime: 0,
    longBreakTime: 0,
    totalSessions: 0,
    moneyInvested: 0,
    moneyWasted: 0
  },
  isActive: false,
  lastUpdateTime: Date.now(),
  dailyStartTime: null,
  lastBalanceChange: 0,
  pomodoroState: {
    completedFocusSessions: 0,
    currentCycle: 1,
    isInBreak: false,
    selectedFocusDuration: 25 // minutes
  },
  overtimeTracking: {
    focusOvertime: 0,
    breakOvertime: 0,
    currentSessionOvertime: 0
  }
};

// Action types
const ACTION_TYPES = {
  START_SESSION: 'START_SESSION',
  END_SESSION: 'END_SESSION',
  UPDATE_BALANCE: 'UPDATE_BALANCE',
  RESET_DAILY: 'RESET_DAILY',
  LOAD_STATE: 'LOAD_STATE',
  UPDATE_STATS: 'UPDATE_STATS',
  UPDATE_OVERTIME: 'UPDATE_OVERTIME'
};

// Reducer
function timeWalletReducer(state, action) {
  switch (action.type) {
    case ACTION_TYPES.START_SESSION:
      return {
        ...state,
        currentSession: action.payload.sessionType,
        sessionStartTime: Date.now(),
        isActive: true,
        lastUpdateTime: Date.now()
      };

    case ACTION_TYPES.END_SESSION:
      if (!state.sessionStartTime) {
        return state; // No session to end
      }

      const sessionDuration = Date.now() - state.sessionStartTime;
      const sessionMinutes = Math.round(sessionDuration / 60000); // Round to nearest minute

      // Determine which time field to update
      let timeField;
      switch (state.currentSession) {
        case SESSION_TYPES.FOCUS:
          timeField = 'focusTime';
          break;
        case SESSION_TYPES.SHORT_BREAK:
          timeField = 'shortBreakTime';
          break;
        case SESSION_TYPES.LONG_BREAK:
          timeField = 'longBreakTime';
          break;
        default:
          timeField = null;
      }

      return {
        ...state,
        currentSession: SESSION_TYPES.IDLE,
        sessionStartTime: null,
        isActive: false,
        dailyStats: {
          ...state.dailyStats,
          ...(timeField && {
            [timeField]: state.dailyStats[timeField] + Math.max(1, sessionMinutes) // Minimum 1 minute
          }),
          totalSessions: state.dailyStats.totalSessions + 1
        },
        pomodoroState: {
          ...state.pomodoroState,
          ...(state.currentSession === SESSION_TYPES.FOCUS && {
            completedFocusSessions: state.pomodoroState.completedFocusSessions + 1
          })
        }
      };

    case ACTION_TYPES.UPDATE_BALANCE:
      return {
        ...state,
        balance: Math.max(0, action.payload.newBalance),
        lastUpdateTime: Date.now(),
        lastBalanceChange: action.payload.deduction,
        dailyStats: {
          ...state.dailyStats,
          moneyInvested: state.currentSession === SESSION_TYPES.FOCUS ?
            state.dailyStats.moneyInvested + action.payload.deduction :
            state.dailyStats.moneyInvested,
          moneyWasted: state.currentSession !== SESSION_TYPES.FOCUS ?
            state.dailyStats.moneyWasted + action.payload.deduction :
            state.dailyStats.moneyWasted
        }
      };

    case ACTION_TYPES.RESET_DAILY:
      const todayStartTime = getTodayStartTime();
      const currentBalance = calculateBalanceFromElapsedTime(todayStartTime);
      const elapsedWastedMoney = calculateWastedMoney(todayStartTime);

      return {
        ...initialState,
        dailyStartTime: todayStartTime,
        balance: currentBalance,
        lastUpdateTime: Date.now(),
        dailyStats: {
          ...initialState.dailyStats,
          moneyWasted: elapsedWastedMoney
        }
      };

    case ACTION_TYPES.LOAD_STATE:
      return {
        ...state,
        ...action.payload
      };

    case ACTION_TYPES.UPDATE_OVERTIME:
      return {
        ...state,
        overtimeTracking: {
          ...state.overtimeTracking,
          ...action.payload
        }
      };

    default:
      return state;
  }
}

// Helper functions
function isWithinDailyHours() {
  const now = new Date();
  const currentHour = now.getHours();
  return currentHour >= DAILY_START_HOUR && currentHour <= DAILY_END_HOUR;
}

function shouldResetDaily(lastUpdateTime) {
  const now = new Date();
  const lastUpdate = new Date(lastUpdateTime);

  // Reset if it's a new day
  return (
    now.getDate() !== lastUpdate.getDate() ||
    now.getMonth() !== lastUpdate.getMonth() ||
    now.getFullYear() !== lastUpdate.getFullYear()
  );
}

function getTodayStartTime() {
  const now = new Date();
  const startTime = new Date(now);
  startTime.setHours(DAILY_START_HOUR, 0, 0, 0);
  return startTime.getTime();
}

function calculateBalanceFromElapsedTime(dailyStartTime) {
  if (!dailyStartTime) {
    // If no daily start time, calculate from today's 6 AM
    dailyStartTime = getTodayStartTime();
  }

  const now = Date.now();
  const elapsedSeconds = Math.max(0, (now - dailyStartTime) / 1000);
  const deductionsToMake = Math.floor(elapsedSeconds / DEDUCTION_INTERVAL);
  const totalDeduction = deductionsToMake * DEDUCTION_AMOUNT;

  return Math.max(0, STARTING_BALANCE - totalDeduction);
}

function calculateWastedMoney(dailyStartTime) {
  if (!dailyStartTime) {
    dailyStartTime = getTodayStartTime();
  }

  const now = Date.now();
  const elapsedSeconds = Math.max(0, (now - dailyStartTime) / 1000);
  const deductionsToMake = Math.floor(elapsedSeconds / DEDUCTION_INTERVAL);
  return deductionsToMake * DEDUCTION_AMOUNT;
}

// Context
const TimeWalletContext = createContext();

// Provider component
export function TimeWalletProvider({ children }) {
  const [state, dispatch] = useReducer(timeWalletReducer, initialState);

  // Load state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem('timeWalletState');
    if (savedState) {
      const parsedState = JSON.parse(savedState);

      // Check if we need to reset for a new day
      if (shouldResetDaily(parsedState.lastUpdateTime)) {
        dispatch({ type: ACTION_TYPES.RESET_DAILY });
      } else {
        // Calculate correct balance based on elapsed time since daily start
        const correctBalance = parsedState.dailyStartTime ?
          calculateBalanceFromElapsedTime(parsedState.dailyStartTime) :
          parsedState.balance;

        // Calculate total wasted money since start of day
        const totalElapsedWastedMoney = STARTING_BALANCE - correctBalance;
        const previousWastedMoney = parsedState.dailyStats?.moneyWasted || 0;
        const adjustedWastedMoney = Math.max(totalElapsedWastedMoney, previousWastedMoney);

        dispatch({
          type: ACTION_TYPES.LOAD_STATE,
          payload: {
            ...parsedState,
            balance: correctBalance,
            lastUpdateTime: Date.now(),
            dailyStats: {
              ...parsedState.dailyStats,
              moneyWasted: adjustedWastedMoney
            }
          }
        });
      }
    } else {
      // First time loading - initialize with correct balance for current time
      dispatch({ type: ACTION_TYPES.RESET_DAILY });
    }
  }, []);

  // Save state to localStorage whenever state changes
  useEffect(() => {
    try {
      const stateToSave = {
        ...state,
        // Don't save temporary UI state
        lastBalanceChange: 0
      };
      localStorage.setItem('timeWalletState', JSON.stringify(stateToSave));

      // Also save daily history
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
      const dailyHistory = JSON.parse(localStorage.getItem('timeWalletHistory') || '{}');
      dailyHistory[today] = {
        date: today,
        dailyStats: state.dailyStats,
        balance: state.balance,
        dailyStartTime: state.dailyStartTime
      };
      localStorage.setItem('timeWalletHistory', JSON.stringify(dailyHistory));
    } catch (error) {
      console.error('Failed to save state to localStorage:', error);
    }
  }, [state]);

  // Balance update timer
  useEffect(() => {
    if (!isWithinDailyHours()) return;

    const interval = setInterval(() => {
      const now = Date.now();
      const timeSinceLastUpdate = (now - state.lastUpdateTime) / 1000; // in seconds
      
      if (timeSinceLastUpdate >= DEDUCTION_INTERVAL) {
        const deductionsToMake = Math.floor(timeSinceLastUpdate / DEDUCTION_INTERVAL);
        const totalDeduction = deductionsToMake * DEDUCTION_AMOUNT;
        
        dispatch({
          type: ACTION_TYPES.UPDATE_BALANCE,
          payload: {
            newBalance: state.balance - totalDeduction,
            deduction: totalDeduction
          }
        });
      }
    }, 1000); // Check every second

    return () => clearInterval(interval);
  }, [state.balance, state.lastUpdateTime, state.currentSession]);

  // Helper function to get historical data
  const getHistoricalData = (date) => {
    try {
      const history = JSON.parse(localStorage.getItem('timeWalletHistory') || '{}');
      return history[date] || null;
    } catch (error) {
      console.error('Failed to load historical data:', error);
      return null;
    }
  };

  // Context value
  const value = {
    ...state,
    startSession: (sessionType, duration) => {
      dispatch({ type: ACTION_TYPES.START_SESSION, payload: { sessionType, duration } });
    },
    endSession: () => {
      dispatch({ type: ACTION_TYPES.END_SESSION });
    },
    resetDaily: () => {
      dispatch({ type: ACTION_TYPES.RESET_DAILY });
    },
    updateOvertime: (overtimeData) => {
      dispatch({ type: ACTION_TYPES.UPDATE_OVERTIME, payload: overtimeData });
    },
    getHistoricalData,
    isWithinDailyHours: isWithinDailyHours(),
    constants: {
      DAILY_START_HOUR,
      DAILY_END_HOUR,
      STARTING_BALANCE,
      DEDUCTION_INTERVAL,
      DEDUCTION_AMOUNT
    }
  };

  return (
    <TimeWalletContext.Provider value={value}>
      {children}
    </TimeWalletContext.Provider>
  );
}

// Custom hook
export function useTimeWallet() {
  const context = useContext(TimeWalletContext);
  if (!context) {
    throw new Error('useTimeWallet must be used within a TimeWalletProvider');
  }
  return context;
}
