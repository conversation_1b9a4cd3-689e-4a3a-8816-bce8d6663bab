* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Modern professional dark theme colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #111111;
  --bg-tertiary: #1a1a1a;
  --bg-quaternary: #222222;
  --bg-elevated: #2a2a2a;
  --text-primary: #ffffff;
  --text-secondary: #e4e4e7;
  --text-tertiary: #a1a1aa;
  --text-muted: #71717a;
  --text-disabled: #52525b;
  --accent-primary: #6366f1;
  --accent-secondary: #8b5cf6;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-error: #ef4444;
  --accent-info: #3b82f6;

  /* Session-specific colors based on reference image */
  --focus-primary: #7c2d12;    /* Dark brown for focus */
  --focus-secondary: #fef7f0;  /* Light pink background */
  --focus-accent: #ff7b34;     /* Orange accent */

  --short-break-primary: #14532d;  /* Dark green */
  --short-break-secondary: #f0fdf4; /* Light green background */
  --short-break-accent: #16a34a;   /* Green accent */

  --long-break-primary: #1e3a8a;   /* Dark blue */
  --long-break-secondary: #eff6ff; /* Light blue background */
  --long-break-accent: #2563eb;    /* Blue accent */

  /* Money colors */
  --money-green: #85ff51;  /* For invested/earned money */
  --money-red: #ff5a5a;    /* For wasted money */
  --border-primary: #27272a;
  --border-secondary: #3f3f46;
  --border-accent: #52525b;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --blur-bg: rgba(17, 17, 17, 0.8);

  color-scheme: dark;
  color: var(--text-primary);
  background-color: var(--bg-primary);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: radial-gradient(ellipse at top, var(--bg-secondary) 0%, var(--bg-primary) 70%);
  overflow-x: hidden;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(16, 185, 129, 0.04) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

#root {
  width: 100%;
  min-height: 100vh;
  position: relative;
  z-index: 1;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Selection styling */
::selection {
  background: var(--accent-blue);
  color: var(--text-primary);
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--accent-blue);
  outline-offset: 2px;
}
